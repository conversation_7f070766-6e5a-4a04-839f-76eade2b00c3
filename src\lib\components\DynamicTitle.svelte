<script>
	import { onDestroy, onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	let useY = $state(false);
	const letterSwapIntervalMs = 4500; // 4.5 seconds for i/y swap

	const words = $state(['anime', 'manga', 'light novel', 'visual novel']);
	let currentWordIndex = $state(0);

	let letterIntervalId;
	let wordChangeIntervalId;

	onMount(() => {
		letterIntervalId = setInterval(() => {
			useY = !useY;
		}, letterSwapIntervalMs);

		// Change words every 3 seconds
		wordChangeIntervalId = setInterval(() => {
			currentWordIndex = (currentWordIndex + 1) % words.length;
		}, 3000);
	});

	onDestroy(() => {
		clearInterval(letterIntervalId);
		clearInterval(wordChangeIntervalId);
	});

	const aniThingFirstPart = 'An';
	const aniThingLastPart = 'thing';
	const aniWhereFirstPart = ' an';
	const aniWhereLastPart = 'where.';
</script>

<h1
	class="dynamic-title text-center text-3xl leading-tight font-bold text-slate-50 select-none xs:text-4xl sm:text-5xl md:text-6xl"
>
	<span class="static-text-part">{aniThingFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
		<span class="letter-placeholder"></span>
		{#key useY + 'anithing'}
			<span
				class="letter-swapper"
				in:fade={{ duration: 600, delay: 100, easing: quintOut }}
				out:fade={{ duration: 500, easing: quintOut }}
				>{#if useY}y{:else}i{/if}</span
			>
		{/key}
	</span>
	<span class="static-text-part">{aniThingLastPart}</span>

	<span class="keyword-box-container block xs:inline">
		{#key currentWordIndex}
			<span
				class="keyword-box"
				in:fade={{ duration: 500, easing: quintOut }}
				out:fade={{ duration: 500, easing: quintOut }}
				role="status"
				aria-live="polite"
				aria-atomic="true"
			>
				{words[currentWordIndex]}
			</span>
		{/key}
	</span><span class="comma-separator block xs:inline">,</span>

	<span class="static-text-part">{aniWhereFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
		<span class="letter-placeholder"></span>
		{#key useY + 'aniwhere'}
			<span
				class="letter-swapper"
				in:fade={{ duration: 600, delay: 100, easing: quintOut }}
				out:fade={{ duration: 500, easing: quintOut }}
				>{#if useY}y{:else}i{/if}</span
			>
		{/key}
	</span>
	<span class="static-text-part">{aniWhereLastPart}</span>
</h1>

<style>
	.static-text-part,
	.letter-swapper,
	.keyword-box {
		display: inline-block;
		vertical-align: middle;
		transform-origin: center;
	}

	.letter-swapper {
		width: 1ch;
		text-align: center;
		position: absolute;
		left: 0;
		top: 0;
		transform-origin: bottom center;
	}

	.letter-container {
		position: relative;
		display: inline-block;
		width: 1ch;
		text-align: center;
		vertical-align: middle;
	}

	.letter-placeholder {
		visibility: hidden;
		width: 1ch;
		display: inline-block;
		content: 'y';
	}

	.keyword-box-container {
		margin-left: 0.1em;
		margin-right: 0.1em;
		position: relative;
		text-align: center;
		vertical-align: middle;
		display: inline-flex;
		align-items: center;
		transform: translateY(1px);
	}

	.keyword-box {
		min-width: 120px;
		padding: 0.3em 0.8em;
		background-color: rgba(0, 0, 0, 0.6);
		border: 1px solid rgba(56, 189, 248, 0.8);
		border-radius: 0.375rem;
		color: rgb(56, 189, 248);
		text-align: center;
		font-weight: 500;
		white-space: nowrap;
		backdrop-filter: blur(4px);
	}

	.dynamic-title {
		margin-top: 0;
		margin-bottom: 0.25em;
		line-height: 1.2;
		display: inline-block;
	}

	.comma-separator {
		display: inline-block;
		position: relative;
		vertical-align: baseline;
		transform: translateY(10px);
		margin: 0 0.05em;
	}

@media (max-width: 480px) {
	.keyword-box {
		min-width: 100px;
		padding: 0.25em 0.6em;
		font-size: 0.9em;
	}

	.comma-separator {
		margin: 0.1em 0;
	}
}
</style>
