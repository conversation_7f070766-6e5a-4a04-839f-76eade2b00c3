<script>
	import { onDestroy, onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	let useY = $state(false);
	const letterSwapIntervalMs = 4500; // 4.5 seconds for i/y swap

	const words = $state(['anime', 'manga', 'light novel', 'visual novel']);
	let currentWordIndex = $state(0);

	let letterIntervalId;
	let wordChangeIntervalId;

	onMount(() => {
		letterIntervalId = setInterval(() => {
			useY = !useY;
		}, letterSwapIntervalMs);

		// Change words every 3 seconds
		wordChangeIntervalId = setInterval(() => {
			currentWordIndex = (currentWordIndex + 1) % words.length;
		}, 3000);
	});

	onDestroy(() => {
		clearInterval(letterIntervalId);
		clearInterval(wordChangeIntervalId);
	});

	const aniThingFirstPart = 'An';
	const aniThingLastPart = 'thing';
	const aniWhereFirstPart = ' an';
	const aniWhereLastPart = 'where.';
</script>

<h1
	class="dynamic-title text-center text-3xl leading-tight font-bold text-slate-50 select-none xs:text-4xl sm:text-5xl md:text-6xl"
>
	<span class="static-text-part">{aniThingFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
		<span class="letter-placeholder"></span>
		{#key useY + 'anithing'}
			<span
				class="letter-swapper"
				in:fade={{ duration: 600, delay: 100, easing: quintOut }}
				out:fade={{ duration: 500, easing: quintOut }}
				>{#if useY}y{:else}i{/if}</span
			>
		{/key}
	</span>
	<span class="static-text-part">{aniThingLastPart}</span>

	<span class="keyword-box-container block">
		<span class="keyword-box" role="status" aria-live="polite" aria-atomic="true">
			{#key currentWordIndex}
				<span
					class="keyword-text"
					in:fade={{ duration: 400, easing: quintOut }}
					out:fade={{ duration: 400, easing: quintOut }}
				>
					{words[currentWordIndex]}
				</span>
			{/key}
		</span>
	</span>,

	<span class="static-text-part">{aniWhereFirstPart}</span><span class="letter-container" style="margin: 0 {useY ? '-0.25em' : '-0.40em'} 0 {useY ? '-0.00em' : '-0.10em'};">
		<span class="letter-placeholder"></span>
		{#key useY + 'aniwhere'}
			<span
				class="letter-swapper"
				in:fade={{ duration: 600, delay: 100, easing: quintOut }}
				out:fade={{ duration: 500, easing: quintOut }}
				>{#if useY}y{:else}i{/if}</span
			>
		{/key}
	</span>
	<span class="static-text-part">{aniWhereLastPart}</span>
</h1>

<style>
	.static-text-part,
	.letter-swapper,
	.keyword-box,
	.keyword-text {
		display: inline-block;
		vertical-align: middle;
		transform-origin: center;
	}

	.letter-swapper {
		width: 1ch;
		text-align: center;
		position: absolute;
		left: 0;
		top: 0;
		transform-origin: bottom center;
	}

	.letter-container {
		position: relative;
		display: inline-block;
		width: 1ch;
		text-align: center;
		vertical-align: middle;
	}

	.letter-placeholder {
		visibility: hidden;
		width: 1ch;
		display: inline-block;
		content: 'y';
	}

	.keyword-box-container {
		margin: 0.3em auto 0.2em auto;
		position: relative;
		text-align: center;
		vertical-align: middle;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
	}

	.keyword-box {
		width: 200px;
		height: 1.8em;
		padding: 0;
		background-color: rgba(0, 0, 0, 0.3);
		border: 1px solid rgba(56, 189, 248, 0.4);
		border-radius: 0.5rem;
		color: rgb(56, 189, 248);
		text-align: center;
		font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', monospace;
		font-weight: 400;
		font-size: 0.9em;
		letter-spacing: 0.05em;
		backdrop-filter: blur(8px);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		overflow: hidden;
	}

	.keyword-text {
		position: absolute;
		width: 100%;
		text-align: center;
		white-space: nowrap;
	}

	.dynamic-title {
		margin-top: 0;
		margin-bottom: 0.25em;
		line-height: 1.2;
		display: inline-block;
	}

	.comma-separator {
		display: block;
		text-align: center;
		margin: 0.1em 0 0.2em 0;
		font-size: 1.2em;
	}

@media (max-width: 640px) {
	.keyword-box {
		width: 180px;
		height: 1.6em;
		font-size: 0.85em;
	}
}

@media (max-width: 480px) {
	.keyword-box {
		width: 160px;
		height: 1.5em;
		font-size: 0.8em;
	}
}
</style>
